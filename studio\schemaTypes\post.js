// schemas/post.js

export default {
  // Machine-readable name
  name: 'post',
  // Human-readable name
  title: 'Post',
  // This is a top-level document in Sanity Studio
  type: 'document',
  
  // These are the fields for each post
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: Rule => Rule.required().error('A title is required.'),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required(),
    },
    {
      name: 'mainImage',
      title: 'Main Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: 'alt',
          title: 'Alternative text',
          type: 'string',
          description: 'Important for SEO and accessibility.',
          validation: Rule => Rule.required(),
        }
      ]
    },
    {
      name: 'categories',
      title: 'Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'category'}}],
      validation: Rule => Rule.required(),
    },
    
    // === THIS IS THE CORE RICH TEXT FIELD ===
    {
      name: 'description',
      title: 'Description (Body Content)',
      type: 'array', // Use 'array' for Portable Text
      of: [
        // TYPE 1: The 'block' type for all text content
        {
          type: 'block',
          styles: [
            {title: 'Normal', value: 'normal'},
            {title: 'H1', value: 'h1'},
            {title: 'H2', value: 'h2'},
            {title: 'H3', value: 'h3'},
            {title: 'H4', value: 'h4'},
            {title: 'H5', value: 'h5'},
            {title: 'H6', value: 'h6'},
            {title: 'Quote', value: 'blockquote'},
          ],
          lists: [
            {title: 'Bullet', value: 'bullet'},
            {title: 'Numbered', value: 'number'},
          ],
          marks: {
            decorators: [
              {title: 'Bold', value: 'strong'},
              {title: 'Italic', value: 'em'},
              {title: 'Underline', value: 'underline'},
            ],
            annotations: [
              {
                name: 'link',
                type: 'object',
                title: 'URL',
                fields: [
                  {
                    title: 'URL',
                    name: 'href',
                    type: 'url',
                    validation: Rule => Rule.uri({
                      scheme: ['http', 'https', 'mailto', 'tel']
                    })
                  }
                ]
              }
            ]
          }
        }, // <--- End of the 'block' type object, comma separates it from the next type

        // TYPE 2: The 'image' type for images in the body
        {
          type: 'image',
          options: {
            hotspot: true,
          },
          fields: [
            {
              name: 'alt',
              type: 'string',
              title: 'Alternative Text',
              description: 'Important for SEO and accessibility. Describe the image.',
              validation: Rule => Rule.required().error('Alt text is required for all images.'),
            },
            {
              name: 'caption',
              type: 'string',
              title: 'Caption',
              description: 'Optional text to display below the image.',
            }
          ]
        }
      ] // <--- End of the 'of' array
    }
  ],
  
  // Controls how documents are displayed in lists
  preview: {
    select: {
      title: 'title',
      media: 'mainImage',
      category: 'categories.0.title',
    },
    prepare(selection) {
      const {title, media, category} = selection
      return {
        title: title,
        subtitle: category ? `Category: ${category}` : 'No category set',
        media: media,
      }
    },
  },
}