import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Questionnaire } from "./types";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { FileUploadButton } from "./FileUploadButton";
import { CreateProfileForm, ProfileFormData } from "./CreateProfileForm";
import { serviceCategories } from "@/components/profile/ProfileServices";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";

// Types
interface QuestionSelectorProps {
  questions: Questionnaire[];
  onValueChange: (
    value: string[],
    files: File[],
    profile?: ProfileFormData,
    services?: string[]
  ) => void;
  isLoading: boolean;
  variant?: "new" | "old";
  hasCustomQuestion?: boolean;
  isCustomJob?: boolean;
}

interface Answer {
  value: string;
  id: number;
}

// Subcomponents
const QuestionOption = ({
  option,
  isSelected,
  onClick,
}: {
  option: string;
  isSelected: boolean;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full text-left px-3 py-2 rounded-md border border-gray-200 dark:border-gray-700 transition-all duration-200",
      isSelected
        ? "border-primary text-white bg-primary"
        : "hover:bg-secondary hover:border-primary/30 hover:text-primary dark:hover:bg-gray-800"
    )}
    aria-selected={isSelected}
  >
    <span className="text-sm">{option}</span>
  </button>
);

const QuestionItem = ({
  question,
  options,
  id,
  selectedValue,
  onSelect,
}: {
  question: string;
  options?: string[];
  id: number;
  selectedValue?: string;
  onSelect: (value: string) => void;
}) => (
  <div className="space-y-4">
    <p className="text-[23px] mb-3 text-gray-700 dark:text-gray-300">
      {question}
    </p>
    <div className="space-y-3">
      {options?.map((option, idx) => (
        <QuestionOption
          key={idx}
          option={option}
          isSelected={option === selectedValue}
          onClick={() => onSelect(option)}
        />
      ))}
    </div>
  </div>
);

const ServicesSelector = ({
  selectedServices,
  onServicesChange,
}: {
  selectedServices: string[];
  onServicesChange: (services: string[]) => void;
}) => {
  const handleServiceToggle = (service: string) => {
    onServicesChange(
      selectedServices.includes(service)
        ? selectedServices.filter((s) => s !== service)
        : [...selectedServices, service]
    );
  };

  return (
    <div className="space-y-4">
      <p className="text-[23px] mb-3 text-gray-700 dark:text-gray-300">
        Welke diensten heeft u nodig voor deze klus?
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {serviceCategories.map((category) => (
          <Popover key={category.name}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start gap-2 h-auto py-3"
              >
                <div className="flex flex-col items-start">
                  <span className="font-medium">{category.name}</span>
                  <span className="text-sm text-muted-foreground">
                    {selectedServices.filter((s) =>
                      category.services.includes(s)
                    ).length || "Geen"}{" "}
                    geselecteerd
                  </span>
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 bg-white">
              <ScrollArea className="pr-4 h-[250px]">
                <div className="space-y-2">
                  {category.services.map((service) => (
                    <div key={service} className="flex items-center space-x-2">
                      <Checkbox
                        id={service}
                        checked={selectedServices.includes(service)}
                        onCheckedChange={() => handleServiceToggle(service)}
                      />
                      <label
                        htmlFor={service}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {service}
                      </label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </PopoverContent>
          </Popover>
        ))}
      </div>

      {selectedServices.length > 0 && (
        <div className="mt-4 p-4 bg-muted rounded-lg">
          <h3 className="font-medium mb-2">Geselecteerde diensten:</h3>
          <div className="flex flex-wrap gap-2">
            {selectedServices.map((service) => (
              <div
                key={service}
                className="bg-background px-2 py-1 rounded-md text-sm flex items-center gap-1"
              >
                {service}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent text-muted-foreground hover:text-foreground transition-colors"
                  onClick={() => handleServiceToggle(service)}
                >
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export function QuestionSelector({
  questions,
  onValueChange,
  isLoading,
  variant = "old",
  hasCustomQuestion = false,
  isCustomJob = false,
}: QuestionSelectorProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const [fileList, setFileList] = useState<File[]>([]);
  const [step, setStep] = useState(1);
  const [showError, setShowError] = useState(false);
  const [answers, setAnswers] = useState<Answer[]>([]);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);

  const originQuestionCount = useMemo(
    () => (hasCustomQuestion ? questions.length - 1 : questions.length),
    [questions.length, hasCustomQuestion]
  );

  const questionCount = useMemo(
    () =>
      variant === "new" ? originQuestionCount + 3 : originQuestionCount + 2,
    [variant, originQuestionCount]
  );

  const { baseQuestions, customQuestion, customQuestionPlaceholder } = useMemo(
    () => ({
      baseQuestions: hasCustomQuestion
        ? questions.slice(0, questions.length - 1)
        : questions,
      customQuestion: hasCustomQuestion
        ? questions[originQuestionCount]?.question
        : null,
      customQuestionPlaceholder: hasCustomQuestion
        ? questions[originQuestionCount]?.placeholder
        : null,
    }),
    [questions, hasCustomQuestion, originQuestionCount]
  );

  const handleSubmit = useCallback(() => {
    const hasCurrentAnswer = answers.find((answer) => answer.id === step);
    const isLastStep = step === questionCount;

    if (variant === "new") {
      if (step + 1 === questionCount || hasCurrentAnswer) {
        setStep((prev) => prev + 1);
        setShowError(false);
      } else {
        setShowError(true);
      }
      return;
    }

    if (isLastStep && answers.find((answer) => answer.id === step - 2)) {
      setShowError(false);
      onValueChange(
        answers.map((a) => a.value),
        fileList,
        undefined,
        selectedServices
      );
    } else if (!isLastStep && hasCurrentAnswer) {
      setStep((prev) => prev + 1);
      setShowError(false);
    } else {
      setShowError(true);
    }
  }, [
    answers,
    fileList,
    onValueChange,
    questionCount,
    step,
    variant,
    selectedServices,
  ]);

  const handleProfileSubmit = useCallback(
    (value: ProfileFormData) => {
      setShowError(false);
      onValueChange(
        answers.map((a) => a.value),
        fileList,
        value,
        selectedServices
      );
    },
    [answers, fileList, onValueChange, selectedServices]
  );

  const handleBack = useCallback(() => {
    if (step === 1) {
      navigate("/banen/new");
      return;
    }
    setStep((prev) => Math.max(1, prev - 1));
    setShowError(false);
  }, [step, navigate]);

  useEffect(() => {
    if (scrollRef.current) {
      window.scrollTo({
        top: scrollRef.current.offsetTop,
        behavior: "smooth",
      });
    }
  }, [step]);

  const handleAnswerSelect = useCallback((id: number, value: string) => {
    setAnswers((prev) => {
      const filtered = prev.filter((item) => item.id !== id);
      return [...filtered, { id, value }];
    });
    setShowError(false);
  }, []);

  return (
    <div
      className="flex flex-col gap-10 w-full sm:px-4"
      style={{
        paddingBottom: `${Math.ceil((window.innerHeight - 550) / 2)}px`,
      }}
    >
      <div className="flex flex-col gap-2">
        <div className="flex flex-col gap-[600px]">
          {baseQuestions.slice(0, step).map((question, index) => (
            <QuestionItem
              key={index}
              {...question}
              selectedValue={answers.find((a) => a.id === question.id)?.value}
              onSelect={(value) => handleAnswerSelect(question.id, value)}
            />
          ))}

          {step === originQuestionCount + 1 && (
            <div className="space-y-2">
              <Label
                htmlFor="description"
                className="text-[23px] text-gray-700 dark:text-gray-300 font-normal"
              >
                {customQuestion || "Beschrijving"}
              </Label>
              <Textarea
                id="description"
                placeholder={
                  customQuestionPlaceholder || "Beschrijf de klus in detail..."
                }
                className="min-h-[250px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus-visible:ring-0 focus:border-primary resize-none text-foreground placeholder:text-muted-foreground/70"
                value={
                  answers.find((item) => item.id === originQuestionCount + 1)
                    ?.value ?? ""
                }
                onChange={(e) =>
                  handleAnswerSelect(originQuestionCount + 1, e.target.value)
                }
              />
            </div>
          )}

          {step === originQuestionCount + 2 && (
            <div className="space-y-8">
              {isCustomJob && (
                <ServicesSelector
                  selectedServices={selectedServices}
                  onServicesChange={setSelectedServices}
                />
              )}
              <FileUploadButton fileList={fileList} setFileList={setFileList} />
            </div>
          )}

          {step === originQuestionCount + 3 && (
            <CreateProfileForm
              onBack={handleBack}
              onSubmit={handleProfileSubmit}
              isLoading={isLoading}
            />
          )}
        </div>
        {showError && (
          <p role="alert" className="text-red-700">
            Selecteer een item.
          </p>
        )}
        <div ref={scrollRef} />
      </div>

      {step !== originQuestionCount + 3 && (
        <div className="flex flex-row gap-4">
          <Button variant="outline" onClick={handleBack} disabled={isLoading}>
            Terug
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="text-white"
          >
            {questionCount === step - 1 ? "Indienen" : "Volgende"}
          </Button>
        </div>
      )}
    </div>
  );
}
