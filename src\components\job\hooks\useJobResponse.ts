import { useState, useEffect } from "react";
import moment from "moment";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const useJobResponse = (jobId: string, status?: string) => {
  const [hasResponded, setHasResponded] = useState(false);
  const [responseCount, setResponseCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingResponse, setIsCheckingResponse] = useState(false);

  const { toast } = useToast();

  useEffect(() => {
    const checkExistingResponse = async () => {
      try {
        setIsCheckingResponse(true);
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          setIsLoading(false);
          return;
        }

        const [responseResult, countResult] = await Promise.all([
          supabase
            .from("job_responses")
            .select("id")
            .eq("job_id", jobId)
            .eq("vakman_id", user.id)
            .maybeSingle(),
          supabase
            .from("job_responses")
            .select("id", { count: "exact" })
            .eq("job_id", jobId),
        ]);

        if (responseResult.error) throw responseResult.error;
        if (countResult.error) throw countResult.error;

        setResponseCount(countResult.count || 0);
        setHasResponded(!!responseResult.data);
      } catch (error) {
        console.error("Error checking response:", error);
        toast({
          variant: "destructive",
          title: "Fout bij controleren reacties",
          description:
            "Er is een probleem opgetreden bij het controleren van bestaande reacties.",
        });
      } finally {
        setIsLoading(false);
        setIsCheckingResponse(false);
      }
    };

    checkExistingResponse();
  }, [jobId, status, toast]);

  const handleJobResponse = async (message: string) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("No authenticated user");

      const {
        data: { created_at },
      } = await supabase
        .from("jobs")
        .select("created_at")
        .eq("id", jobId)
        .single();

      const { error } = await supabase.from("job_responses").insert({
        job_id: jobId,
        vakman_id: user.id,
        message: message || "Ik heb interesse in deze klus.",
        response_time_minutes: moment().diff(moment(created_at), "minutes"),
      });

      if (error) throw error;

      setHasResponded(true);

      // We don't need to check response_time_minutes anymore as the bonus is handled by the database trigger
      toast({
        title: "Succesvol gereageerd",
        description:
          "Je reactie is succesvol verstuurd. Als je snel hebt gereageerd, ontvang je automatisch extra credits!",
      });
    } catch (error: any) {
      console.error("Error responding to job:", error);
      toast({
        variant: "destructive",
        title: "Fout bij reageren",
        description:
          error.message ||
          "Er is een probleem opgetreden bij het reageren op de klus.",
      });
      throw error;
    }
  };

  return {
    hasResponded,
    responseCount,
    isLoading,
    isCheckingResponse,
    handleJobResponse,
  };
};
