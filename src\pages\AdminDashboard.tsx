import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import {
  Database,
  BarChart3,
  FileText,
  MessageSquare,
  Map,
  Building,
  Bot,
  Hammer,
  UserCog,
} from "lucide-react";

const AdminDashboard = () => {
  const navigate = useNavigate();

  const adminSections = [
    {
      title: "Database",
      description: "Bekijk en beheer database statistieken",
      icon: <Database className="h-6 w-6 text-white" />,
      path: "/beheerder/databank",
      gradient: "from-blue-500 to-blue-600",
    },
    {
      title: "Analytics",
      description: "Bekijk platform statistieken",
      icon: <BarChart3 className="h-6 w-6 text-white" />,
      path: "/beheerder/analytisch",
      gradient: "from-green-500 to-green-600",
    },
    {
      title: "Rapportages",
      description: "Genereer en bekijk rapportages",
      icon: <FileText className="h-6 w-6 text-white" />,
      path: "/beheerder/rapporten",
      gradient: "from-purple-500 to-purple-600",
    },
    {
      title: "Klussen",
      description: "Klussen beheren",
      icon: <MessageSquare className="h-6 w-6 text-white" />,
      path: "/beheerder/berichten",
      gradient: "from-yellow-500 to-yellow-600",
    },
    {
      title: "Vakmannen Map",
      description: "Bekijk vakmannen op de kaart",
      icon: <Map className="h-6 w-6 text-white" />,
      path: "/beheerder/vakmannen-map",
      gradient: "from-orange-500 to-orange-600",
    },
    {
      title: "Company Forms",
      description: "Beheer bedrijfsaanvragen",
      icon: <Building className="h-6 w-6 text-white" />,
      path: "/beheerder/bedrijfsaanvragen",
      gradient: "from-indigo-500 to-indigo-600",
    },
    {
      title: "Vakmannen",
      description: "Beheer vakmannen",
      icon: <Hammer className="h-6 w-6 text-white" />,
      path: "/beheerder/vakmannen",
      gradient: "from-orange-500 to-orange-600",
    },
    {
      title: "Klusaanvrager",
      description: "Beheer klusaanvrager",
      icon: <UserCog className="h-6 w-6 text-white" />,
      path: "/beheerder/klusaanvrager",
      gradient: "from-cyan-500 to-cyan-600",
    },

    {
      title: "Job Templates",
      description: "Beheer bot klussjablonen",
      icon: <Bot className="h-6 w-6 text-white" />,
      path: "/beheerder/klussjablonen",
      gradient: "from-red-500 to-red-600",
    },
  ];

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-0 px-6">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adminSections.map((section) => (
          <Card
            key={section.title}
            className="p-6 cursor-pointer hover:shadow-lg gap-4 flex transition-shadow"
            onClick={() => handleNavigate(section.path)}
          >
            <div
              className={`inline-flex p-3 rounded-lg h-fit bg-gradient-to-br ${section.gradient} mb-4`}
            >
              {section.icon}
            </div>
            <div>
              <h2 className="text-lg font-semibold mb-2">{section.title}</h2>
              <p className="text-gray-600 dark:text-gray-300">
                {section.description}
              </p>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AdminDashboard;
