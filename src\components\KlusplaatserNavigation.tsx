import { Menu, Plus, List, User, LayoutDashboard, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSessionHandler } from "@/hooks/useSessionHandler";

export const KlusplaatserNavigation = () => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { handleLogout: handleSessionLogout } = useSessionHandler();

  const handleNavigation = (view: string) => {
    switch (view) {
      case "dashboard":
        navigate("/");
        break;
      case "new-job":
        navigate("/banen/new");
        break;
      case "my-jobs":
        navigate("/banen");
        break;
      case "profile":
        navigate("/profiel");
        break;
      default:
        navigate("/");
    }
  };

  return (
    <nav className="w-full sm:w-auto flex justify-end items-center gap-2">
      {!isMobile && (
        <Button
          className="hidden sm:flex items-center gap-2 text-white"
          onClick={() => handleNavigation("dashboard")}
        >
          <LayoutDashboard className="h-4 w-4 text-white" />
          Dashboard
        </Button>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9"
            aria-label="Open Menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="w-56 bg-card/95 dark:bg-slate-900/95 backdrop-blur-sm border border-border/50 shadow-lg"
        >
          <DropdownMenuItem
            onClick={() => handleNavigation("dashboard")}
            className="hover:!bg-muted !text-black py-3"
          >
            <LayoutDashboard className="mr-3 h-4 w-4" />
            Dashboard
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleNavigation("new-job")}
            className="hover:!bg-muted !text-black py-3"
          >
            <Plus className="mr-3 h-4 w-4" />
            Nieuwe Klus
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleNavigation("my-jobs")}
            className="hover:!bg-muted !text-black py-3"
          >
            <List className="mr-3 h-4 w-4" />
            Mijn Klussen
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleNavigation("profile")}
            className="hover:!bg-muted !text-black py-3"
          >
            <User className="mr-3 h-4 w-4" />
            Profiel
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={handleSessionLogout}
            className="hover:!bg-muted py-3 !text-red-500"
          >
            <LogOut className="mr-3 h-4 w-4" />
            Uitloggen
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </nav>
  );
};
