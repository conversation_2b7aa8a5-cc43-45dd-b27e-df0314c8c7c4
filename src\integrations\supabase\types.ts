export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      admin_logs: {
        Row: {
          action: string;
          admin_id: string;
          created_at: string;
          details: J<PERSON>;
          id: string;
        };
        Insert: {
          action: string;
          admin_id: string;
          created_at?: string;
          details: Json;
          id?: string;
        };
        Update: {
          action?: string;
          admin_id?: string;
          created_at?: string;
          details?: Json;
          id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "admin_logs_admin_id_fkey";
            columns: ["admin_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      admin_settings: {
        Row: {
          created_at: string;
          id: string;
          key: string;
          updated_at: string;
          value: Json;
        };
        Insert: {
          created_at?: string;
          id?: string;
          key: string;
          updated_at?: string;
          value: Json;
        };
        Update: {
          created_at?: string;
          id?: string;
          key?: string;
          updated_at?: string;
          value?: Json;
        };
        Relationships: [];
      };
      balance_transactions: {
        Row: {
          amount: number;
          completed_at: string | null;
          created_at: string;
          description: string | null;
          id: string;
          invoice_url: string | null;
          mollie_payment_id: string | null;
          pdf_url: string | null;
          status: string | null;
          type: string | null;
          user_id: string;
        };
        Insert: {
          amount: number;
          completed_at?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          invoice_url?: string | null;
          mollie_payment_id?: string | null;
          pdf_url?: string | null;
          status?: string | null;
          type?: string | null;
          user_id: string;
        };
        Update: {
          amount?: number;
          completed_at?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          invoice_url?: string | null;
          mollie_payment_id?: string | null;
          pdf_url?: string | null;
          status?: string | null;
          type?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "balance_transactions_user_id_fkey1";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      chats: {
        Row: {
          client_id: string | null;
          craftman_id: string | null;
          created_at: string;
          id: number;
          job_id: string | null;
          slug: string | null;
          status: string;
        };
        Insert: {
          client_id?: string | null;
          craftman_id?: string | null;
          created_at?: string;
          id?: number;
          job_id?: string | null;
          slug?: string | null;
          status?: string;
        };
        Update: {
          client_id?: string | null;
          craftman_id?: string | null;
          created_at?: string;
          id?: number;
          job_id?: string | null;
          slug?: string | null;
          status?: string;
        };
        Relationships: [
          {
            foreignKeyName: "chats_client_id_fkey";
            columns: ["client_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "chats_craftman_id_fkey";
            columns: ["craftman_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "chats_job_id_fkey";
            columns: ["job_id"];
            isOneToOne: false;
            referencedRelation: "jobs";
            referencedColumns: ["id"];
          }
        ];
      };
      company_forms: {
        Row: {
          company_name: string | null;
          created_at: string;
          id: number;
          kvk_number: string | null;
          name: string | null;
          personnel_count: string | null;
          personnel_type: string | null;
        };
        Insert: {
          company_name?: string | null;
          created_at?: string;
          id?: number;
          kvk_number?: string | null;
          name?: string | null;
          personnel_count?: string | null;
          personnel_type?: string | null;
        };
        Update: {
          company_name?: string | null;
          created_at?: string;
          id?: number;
          kvk_number?: string | null;
          name?: string | null;
          personnel_count?: string | null;
          personnel_type?: string | null;
        };
        Relationships: [];
      };
      email_logs: {
        Row: {
          email_type: string;
          error: string | null;
          id: string;
          sent_at: string | null;
          status: string | null;
          user_id: string;
        };
        Insert: {
          email_type: string;
          error?: string | null;
          id?: string;
          sent_at?: string | null;
          status?: string | null;
          user_id: string;
        };
        Update: {
          email_type?: string;
          error?: string | null;
          id?: string;
          sent_at?: string | null;
          status?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      job_responses: {
        Row: {
          created_at: string;
          id: string;
          job_id: string;
          message: string | null;
          response_time_minutes: number | null;
          status: string;
          updated_at: string;
          vakman_id: string;
          viewed_at: string | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          job_id: string;
          message?: string | null;
          response_time_minutes?: number | null;
          status?: string;
          updated_at?: string;
          vakman_id: string;
          viewed_at?: string | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          job_id?: string;
          message?: string | null;
          response_time_minutes?: number | null;
          status?: string;
          updated_at?: string;
          vakman_id?: string;
          viewed_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "job_responses_job_id_fkey";
            columns: ["job_id"];
            isOneToOne: false;
            referencedRelation: "jobs";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "job_responses_vakman_id_fkey";
            columns: ["vakman_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      job_schedules: {
        Row: {
          description: string | null;
          id: number;
          photos: string[] | null;
          title: string | null;
          services: string[];
        };
        Insert: {
          description?: string | null;
          id?: number;
          photos?: string[] | null;
          title?: string | null;
          services?: string[];
        };
        Update: {
          description?: string | null;
          id?: number;
          photos?: string[] | null;
          title?: string | null;
          services?: string[];
        };
        Relationships: [];
      };
      jobs: {
        Row: {
          budget: number | null;
          created_at: string;
          deleted_at: string | null;
          description: string;
          details: Json | null;
          direct_request: string[] | null;
          first_craftsman_accepted_at: string | null;
          hired_craftman_id: string | null;
          house_number: string | null;
          house_number_addition: string | null;
          id: string;
          is_bot: boolean;
          photos: Json | null;
          postal_code: string | null;
          response_cost: number | null;
          status: Database["public"]["Enums"]["job_status"];
          title: string;
          user_id: string;
          services: string[];
        };
        Insert: {
          budget?: number | null;
          created_at?: string;
          deleted_at?: string | null;
          description: string;
          details?: Json | null;
          direct_request?: string[] | null;
          first_craftsman_accepted_at?: string | null;
          hired_craftman_id?: string | null;
          house_number?: string | null;
          house_number_addition?: string | null;
          id?: string;
          is_bot?: boolean;
          photos?: Json | null;
          postal_code?: string | null;
          response_cost?: number | null;
          status?: Database["public"]["Enums"]["job_status"];
          title: string;
          user_id: string;
          services: string[];
        };
        Update: {
          budget?: number | null;
          created_at?: string;
          deleted_at?: string | null;
          description?: string;
          details?: Json | null;
          direct_request?: string[] | null;
          first_craftsman_accepted_at?: string | null;
          hired_craftman_id?: string | null;
          house_number?: string | null;
          house_number_addition?: string | null;
          id?: string;
          is_bot?: boolean;
          photos?: Json | null;
          postal_code?: string | null;
          response_cost?: number | null;
          status?: Database["public"]["Enums"]["job_status"];
          title?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "jobs_hired_craftman_id_fkey";
            columns: ["hired_craftman_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "jobs_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      messages: {
        Row: {
          attachment_type: string | null;
          attachment_url: string | null;
          chat_id: string | null;
          content: string;
          created_at: string;
          id: string;
          job_id: string;
          read: boolean | null;
          receiver_id: string;
          sender_id: string;
        };
        Insert: {
          attachment_type?: string | null;
          attachment_url?: string | null;
          chat_id?: string | null;
          content: string;
          created_at?: string;
          id?: string;
          job_id: string;
          read?: boolean | null;
          receiver_id: string;
          sender_id: string;
        };
        Update: {
          attachment_type?: string | null;
          attachment_url?: string | null;
          chat_id?: string | null;
          content?: string;
          created_at?: string;
          id?: string;
          job_id?: string;
          read?: boolean | null;
          receiver_id?: string;
          sender_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "messages_chat_id_fkey";
            columns: ["chat_id"];
            isOneToOne: false;
            referencedRelation: "chats";
            referencedColumns: ["slug"];
          },
          {
            foreignKeyName: "messages_job_id_fkey";
            columns: ["job_id"];
            isOneToOne: false;
            referencedRelation: "jobs";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "messages_receiver_id_fkey";
            columns: ["receiver_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "messages_sender_id_fkey";
            columns: ["sender_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      portfolio_projects: {
        Row: {
          budget: number | null;
          created_at: string;
          description: string | null;
          id: string;
          photos: Json | null;
          title: string;
          updated_at: string;
          user_id: string | null;
        };
        Insert: {
          budget?: number | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          photos?: Json | null;
          title: string;
          updated_at?: string;
          user_id?: string | null;
        };
        Update: {
          budget?: number | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          photos?: Json | null;
          title?: string;
          updated_at?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "portfolio_projects_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          balance: number | null;
          btw_number: string | null;
          city: string | null;
          company_name: string | null;
          created_at: string;
          diplomas: Json[] | null;
          email: string | null;
          first_name: string | null;
          full_name: string | null;
          house_number: string | null;
          house_number_addition: string | null;
          id: string;
          kvk_number: string | null;
          last_name: string | null;
          phone_number: string | null;
          postal_code: string | null;
          profile_photo_url: string | null;
          services: string[] | null;
          status: Database["public"]["Enums"]["profile_status"];
          street_address: string | null;
          updated_at: string;
          user_type: string | null;
        };
        Insert: {
          balance?: number | null;
          btw_number?: string | null;
          city?: string | null;
          company_name?: string | null;
          created_at?: string;
          diplomas?: Json[] | null;
          email?: string | null;
          first_name?: string | null;
          full_name?: string | null;
          house_number?: string | null;
          house_number_addition?: string | null;
          id: string;
          kvk_number?: string | null;
          last_name?: string | null;
          phone_number?: string | null;
          postal_code?: string | null;
          profile_photo_url?: string | null;
          services?: string[] | null;
          status?: Database["public"]["Enums"]["profile_status"];
          street_address?: string | null;
          updated_at?: string;
          user_type?: string | null;
        };
        Update: {
          balance?: number | null;
          btw_number?: string | null;
          city?: string | null;
          company_name?: string | null;
          created_at?: string;
          diplomas?: Json[] | null;
          email?: string | null;
          first_name?: string | null;
          full_name?: string | null;
          house_number?: string | null;
          house_number_addition?: string | null;
          id?: string;
          kvk_number?: string | null;
          last_name?: string | null;
          phone_number?: string | null;
          postal_code?: string | null;
          profile_photo_url?: string | null;
          services?: string[] | null;
          status?: Database["public"]["Enums"]["profile_status"];
          street_address?: string | null;
          updated_at?: string;
          user_type?: string | null;
        };
        Relationships: [];
      };
      referrals: {
        Row: {
          bonus_paid: boolean | null;
          completed_at: string | null;
          created_at: string;
          id: string;
          referred_email: string;
          referred_user_id: string | null;
          referrer_id: string;
          status: string;
        };
        Insert: {
          bonus_paid?: boolean | null;
          completed_at?: string | null;
          created_at?: string;
          id?: string;
          referred_email: string;
          referred_user_id?: string | null;
          referrer_id: string;
          status?: string;
        };
        Update: {
          bonus_paid?: boolean | null;
          completed_at?: string | null;
          created_at?: string;
          id?: string;
          referred_email?: string;
          referred_user_id?: string | null;
          referrer_id?: string;
          status?: string;
        };
        Relationships: [
          {
            foreignKeyName: "referrals_referred_user_id_fkey";
            columns: ["referred_user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "referrals_referrer_id_fkey";
            columns: ["referrer_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      review_responses: {
        Row: {
          created_at: string;
          id: string;
          response: string;
          review_id: string;
          updated_at: string;
          vakman_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          response: string;
          review_id: string;
          updated_at?: string;
          vakman_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          response?: string;
          review_id?: string;
          updated_at?: string;
          vakman_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "review_responses_review_id_fkey";
            columns: ["review_id"];
            isOneToOne: true;
            referencedRelation: "vakman_reviews";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "review_responses_vakman_id_fkey";
            columns: ["vakman_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      vakman_reviews: {
        Row: {
          comment: string | null;
          created_at: string;
          feedback: string | null;
          id: string;
          job_id: string;
          rating: number;
          reviewer_id: string;
          vakman_id: string;
        };
        Insert: {
          comment?: string | null;
          created_at?: string;
          feedback?: string | null;
          id?: string;
          job_id: string;
          rating: number;
          reviewer_id: string;
          vakman_id: string;
        };
        Update: {
          comment?: string | null;
          created_at?: string;
          feedback?: string | null;
          id?: string;
          job_id?: string;
          rating?: number;
          reviewer_id?: string;
          vakman_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "vakman_reviews_job_id_fkey";
            columns: ["job_id"];
            isOneToOne: false;
            referencedRelation: "jobs";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "vakman_reviews_reviewer_id_fkey";
            columns: ["reviewer_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "vakman_reviews_vakman_id_fkey";
            columns: ["vakman_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      check_cron_job_exists: {
        Args: { job_name: string };
        Returns: boolean;
      };
      create_random_scheduled_job: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      get_cron_job_schedule: {
        Args: { job_name: string };
        Returns: string;
      };
      increment_balance: {
        Args: { user_id: string; increment_amount: number };
        Returns: undefined;
      };
      schedule_cron_job: {
        Args: { job_name: string; cron_expression: string; command: string };
        Returns: string;
      };
      unschedule_cron_job: {
        Args: { job_name: string };
        Returns: string;
      };
      get_filtered_vakmannen: {
        Args: { job_id: string };
        Returns: Array<{
          id: string;
          company_name: string | null;
          email: string | null;
          user_type: string | null;
          profile_photo_url: string | null;
          first_name: string | null;
          last_name: string | null;
          phone_number: string | null;
          kvk_number: string | null;
          btw_number: string | null;
          street_address: string | null;
          city: string | null;
          postal_code: string | null;
          status: string | null;
          diplomas: any[];
          services: string[] | null;
          average_rating: number | null;
          total_reviews: number | null;
          portfolio_project_count: number | null;
        }>;
      };
    };
    Enums: {
      job_status: "pending" | "open" | "in_progress" | "completed";
      profile_status: "in_review" | "active" | "inactive";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      job_status: ["pending", "open", "in_progress", "completed"],
      profile_status: ["in_review", "active", "inactive"],
    },
  },
} as const;
