import { Link } from "react-router-dom";
import { Menu } from "lucide-react";
import { useState } from "react";

import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";

export const PublicLayout = ({ children }: { children: React.ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuItems = [
    {
      label: "Personeel vinden",
      path: "/personeel-vinden",
    },
    {
      label: "Plaats een klus",
      path: "/plaats-een-klus",
    },
  ];
  const isJobCreatePage = location.pathname.includes("/plaats-een-klus/");
  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="border-b border-b-[#CCCCD9] bg-white">
        <div className="mx-auto max-w-6xl py-2 px-4 sm:px-0">
          <div className="flex items-center justify-between h-20">
            <Link to="/" className="flex items-center gap-2">
              <img
                src="/logo.png"
                alt="Klusgebied Logo"
                className="w-12 h-12"
              />
              <div className="flex flex-col">
                <span className="text-xl font-semibold text-gray-900">
                  Klusgebied
                </span>
                <span className="text-sm text-gray-500">
                  Klussenmarkt van nederland
                </span>
              </div>
            </Link>

            <div className="flex items-center gap-4">
              {/* Desktop Navigation */}
              <NavigationMenu className="hidden md:flex">
                <NavigationMenuList>
                  {menuItems.map((item) => (
                    <NavigationMenuItem key={item.path}>
                      <Link
                        to={item.path}
                        className="px-4 py-2 text-sm font-medium text-gray-900 hover:text-primary transition-colors"
                      >
                        {item.label}
                      </Link>
                    </NavigationMenuItem>
                  ))}
                </NavigationMenuList>
              </NavigationMenu>

              {/* Sign Up Button */}
              <Link
                to="/auth"
                className="hidden md:inline-flex px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors"
              >
                Inschrijven als vakman
              </Link>

              {/* Mobile Menu */}
              <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetTrigger asChild className="md:hidden">
                  <Button variant="ghost" size="icon" aria-label="Open Menu">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                  <SheetHeader>
                    <SheetTitle>Menu</SheetTitle>
                  </SheetHeader>
                  <nav className="flex flex-col gap-4 mt-8">
                    {menuItems.map((item) => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={cn(
                          "text-lg font-medium text-gray-700 hover:text-primary transition-colors",
                          "py-2 px-4 rounded-lg hover:bg-gray-100"
                        )}
                        onClick={() => setIsOpen(false)}
                      >
                        {item.label}
                      </Link>
                    ))}
                    <Link
                      to="/auth"
                      className="text-lg font-medium text-white bg-primary hover:bg-primary/90 py-2 px-4 rounded-lg transition-colors"
                      onClick={() => setIsOpen(false)}
                    >
                      Inschrijven als vakman
                    </Link>
                  </nav>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1">
        <div className="w-full">{children}</div>
      </main>

      {/* Footer */}
      {!isJobCreatePage && (
        <footer className="bg-gray-900 text-white py-12">
          <div className="container mx-auto max-w-5xl px-4 py-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <p className="text-lg font-semibold mb-4">Contact</p>
                <p className="text-gray-400">
                  Email: <EMAIL>
                  <br />
                  Tel: +31 6 84614705
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Links</h3>
                <ul className="space-y-2 text-gray-400">
                  <li>
                    <Link to="/plaats-een-klus" className="hover:text-primary">
                      Onze werkwijze
                    </Link>
                  </li>
                  <li>
                    <Link to="/inloggen" className="hover:text-primary">
                      Waarom Klusgebied
                    </Link>
                  </li>
                  <li>
                    <Link to="/ventilatie" className="hover:text-primary">
                      Ventilatie
                    </Link>
                  </li>
                  <li>
                    <Link to="/ikea-meubels" className="hover:text-primary">
                      IKEA Montage
                    </Link>
                  </li>
                  <li>
                    <Link to="/dakkapel" className="hover:text-primary">
                      Dakkapel
                    </Link>
                  </li>
                  <li>
                    <Link to="/badkamer" className="hover:text-primary">
                      Badkamer
                    </Link>
                  </li>
                  <li>
                    <Link to="/lekkage" className="hover:text-primary">
                      Lekkage
                    </Link>
                  </li>
                  <li>
                    <Link to="/isolatie" className="hover:text-primary">
                      Isolatie
                    </Link>
                  </li>
                  <li>
                    <Link to="/traprenovatie" className="hover:text-primary">
                      Traprenovatie
                    </Link>
                  </li>
                  <li>
                    <Link to="/cv-ketel" className="hover:text-primary">
                      CV-ketel
                    </Link>
                  </li>
                  <li>
                    <Link to="/tuinbestrating" className="hover:text-primary">
                      Tuinbestrating
                    </Link>
                  </li>
                  <li>
                    <Link to="/aannemer" className="hover:text-primary">
                      Aannemer
                    </Link>
                  </li>
                  <li>
                    <Link to="/bouwbedrijf" className="hover:text-primary">
                      Bouwbedrijf
                    </Link>
                  </li>
                  <li>
                    <Link to="/dakbedekking" className="hover:text-primary">
                      Dakbedekking
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/waterleiding-vervangen"
                      className="hover:text-primary"
                    >
                      Waterleiding Vervangen
                    </Link>
                  </li>
                </ul>
              </div>
              <div>
                <p className="text-lg font-semibold mb-4">Over Klusgebied</p>
                <p className="text-gray-400">
                  Klusgebied verbindt vakmannen met klussen in hun regio. Wij
                  maken het vinden van de juiste vakman eenvoudig en
                  betrouwbaar.
                </p>
              </div>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-800 text-center text-gray-400">
              <p>
                &copy; {new Date().getFullYear()} Klusgebied. Alle rechten
                voorbehouden.
              </p>
            </div>
          </div>
        </footer>
      )}
    </div>
  );
};
