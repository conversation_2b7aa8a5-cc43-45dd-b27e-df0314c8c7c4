// schemas/rawHtml.js

export default {
  name: 'rawHtml',
  title: 'Raw HTML',
  type: 'object',
  fields: [
    {
      name: 'html',
      title: 'HTML Code',
      type: 'text',
      description: 'WARNING: This code will be injected directly into the page. Use with caution.',
      // You can add validation if you want, but it's often not necessary here
    },
  ],
  // This helps make it look nice in the editor
  preview: {
    select: {
      title: 'html',
    },
    prepare({ title }) {
      // Show a snippet of the code in the preview
      const snippet = title ? title.substring(0, 50) + '...' : 'No HTML code yet';
      return {
        title: 'Raw HTML Block',
        subtitle: snippet,
      };
    },
  },
};