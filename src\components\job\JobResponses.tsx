import { useEffect, useState } from "react";
import { useSet<PERSON>tom } from "jotai";

import { supabase } from "@/integrations/supabase/client";
import { VakmanProfileCard } from "./VakmanProfileCard";
import { MobileReactionButton } from "./MobileReactionButton";
import { acceptedCraftmanCountAtom } from "@/states/job";

interface JobResponsesProps {
  jobId: string;
  status: string;
  isOwner?: boolean;
  handleComplete?: (hired_craftman_id: string) => Promise<void>;
}

export const JobResponses = ({
  jobId,
  status,
  isOwner = false,
  handleComplete,
}: JobResponsesProps) => {
  const setAcceptedCraftmanCount = useSetAtom(acceptedCraftmanCountAtom);

  const [responses, setResponses] = useState<any[]>([]);
  const [hasResponded, setHasResponded] = useState(false);
  const [showResponses, setShowResponses] = useState(false);

  useEffect(() => {
    const fetchResponses = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return;

        // Check user's response status
        const { data: userResponse } = await supabase
          .from("job_responses")
          .select("id")
          .match({ job_id: jobId, vakman_id: user.id })
          .maybeSingle();

        setHasResponded(Boolean(userResponse));

        // Define status filters
        const statusFilters = {
          completed: "accepted",
          in_behandeling: "accepted",
          accepted: "accepted",
          open: "pending",
        };

        // Build base query
        const baseQuery = {
          job_id: jobId,
          ...(statusFilters[status as keyof typeof statusFilters] && {
            status: statusFilters[status as keyof typeof statusFilters],
          }),
          ...(isOwner ? {} : { vakman_id: user.id }),
        };

        // Fetch job responses
        const { data: jobResponses, error } = await supabase
          .from("job_responses")
          .select(
            `
            id,
            status,
            message,
            created_at,
            profiles:vakman_id (
              id,
              first_name,
              last_name,
              email,
              phone_number,
              profile_photo_url,
              company_name,
              kvk_number,
              btw_number
            )
          `
          )
          .match(baseQuery)
          .order("created_at");

        if (error) throw error;

        const responses = jobResponses || [];
        setResponses(responses);

        const acceptedCount = responses.filter(
          (response) => response.status === "accepted"
        ).length;
        setAcceptedCraftmanCount(acceptedCount);
      } catch (error) {
        console.error("Error fetching responses:", error);
      }
    };

    fetchResponses();
  }, [jobId, status, isOwner]);

  // Don't show anything if there are no responses and user hasn't responded
  if (!responses.length && !hasResponded) {
    return null;
  }

  const getTitle = () => {
    switch (status) {
      case "completed":
        return "Klus uitgevoerd door";
      case "open":
        return hasResponded
          ? "U heeft gereageerd op deze klus"
          : "Reacties van vakmannen";
      case "in_behandeling":
      case "accepted":
        return "Gekozen vakman";
      default:
        return "";
    }
  };

  return (
    <>
      <div className={`space-y-4 ${!showResponses ? "hidden md:block" : ""}`}>
        <h2 className="text-lg font-semibold">{getTitle()}</h2>
        <div className="space-y-4">
          {responses.map((response) => (
            <VakmanProfileCard
              key={response.id}
              profile={response.profiles}
              jobId={jobId}
              showAcceptButton={isOwner && response.status === "pending"}
              status={status}
              isOwner={isOwner}
              handleComplete={handleComplete}
              isAccepted={response.status === "accepted"}
            />
          ))}
        </div>
      </div>

      {/* Mobile Reaction Button */}
      {responses.length > 0 && (
        <MobileReactionButton
          count={responses.length}
          onClick={() => setShowResponses(!showResponses)}
          isShow={showResponses}
        />
      )}
    </>
  );
};
