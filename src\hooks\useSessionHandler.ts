import { useNavigate } from "react-router-dom";

import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/hooks/useAuth";

export const useSessionHandler = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { emptyUserProfile } = useAuth();

  const handleLogout = async () => {
    try {
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError) {
        console.error("Session check error:", sessionError);
        handleSessionCleanup();
        return;
      }

      if (!session) {
        handleSessionCleanup(
          "Sessie verlopen",
          "Je sessie was al verlopen. Je bent nu uitgelogd."
        );
        return;
      }

      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error("Logout error:", error);
        if (error.message.includes("session_not_found")) {
          handleSessionCleanup();
          return;
        }
        toast({
          variant: "destructive",
          title: "Uitloggen mislukt",
          description:
            "Er is een fout opgetreden bij het uitloggen. Probeer het opnieuw.",
        });
        return;
      }

      handleSessionCleanup();
    } catch (error) {
      console.error("Unexpected error during logout:", error);
      handleSessionCleanup(
        "Uitgelogd",
        "Je bent uitgelogd vanwege een onverwachte fout."
      );
    }
  };

  const handleSessionCleanup = (
    title = "Uitgelogd",
    description = "Je bent succesvol uitgelogd."
  ) => {
    emptyUserProfile();
    localStorage.clear();
    navigate("/auth");
    toast({ title, description });
  };

  return { handleLogout };
};
