import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Button } from "@/components/ui/button";

export const BackToDashboard = () => {
  const navigate = useNavigate();

  const handleNavigate = () => {
    navigate("/");
  };

  return (
    <Button
      variant="ghost"
      onClick={handleNavigate}
      className="mb-4 flex items-center gap-2 hover:bg-primary border border-primary"
    >
      <ArrowLeft className="h-4 w-4" />
      Terug naar Dashboard
    </Button>
  );
};
