import { NavigateFunction } from "react-router-dom";
import { LogOut, Briefcase, Euro, User } from "lucide-react";

import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

interface MenuItemsProps {
  onLogout: () => void;
  navigate: NavigateFunction;
}

export const MenuItems = ({ onLogout, navigate }: MenuItemsProps) => {
  const handleNavigation = (view: string) => {
    switch (view) {
      case "dashboard":
        navigate("/");
        break;
      case "available-jobs":
        navigate("/banen");
        break;
      case "balance":
        navigate("/evenwicht");
        break;
      case "profile":
        navigate("/profiel");
        break;
      default:
        navigate("/");
    }
  };

  return (
    <>
      <DropdownMenuItem
        onClick={() => handleNavigation("available-jobs")}
        className="hover:!bg-muted !text-black py-3"
      >
        <Briefcase className="mr-3 h-4 w-4" />
        <PERSON><PERSON><PERSON><PERSON><PERSON>
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => handleNavigation("balance")}
        className="hover:!bg-muted !text-black py-3"
      >
        <Euro className="mr-3 h-4 w-4" />
        Saldo
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => handleNavigation("profile")}
        className="hover:!bg-muted !text-black py-3"
      >
        <User className="mr-3 h-4 w-4" />
        Profiel
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem
        onClick={onLogout}
        className="hover:!bg-muted py-3 !text-red-500"
      >
        <LogOut className="mr-3 h-4 w-4" />
        Uitloggen
      </DropdownMenuItem>
    </>
  );
};
